import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import Product from 'App/Models/Product'
import Category from 'App/Models/Category'
import Page from 'App/Models/Page'
import Database from '@ioc:Adonis/Lucid/Database'
import { meiliDB } from 'App/Plugins/MeiliSearch'

const BASE_URL = 'https://mirsalnikov.ru'
const SITEMAP_PAGE_SIZE = 50_000
const MEILI_CHUNK_SIZE = 1000 // максимальный лимит MeiliSearch на запрос

const createUrlset = (content: string): string =>
  `<?xml version="1.0" encoding="UTF-8"?>\n<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">${content}\n</urlset>`

const createUrlEntry = (
  loc: string,
  lastmod: string,
  changefreq: 'daily' | 'weekly' | 'monthly',
  priority: number
): string => `
  <url>
    <loc>${loc}</loc>
    <lastmod>${lastmod}</lastmod>
    <changefreq>${changefreq}</changefreq>
    <priority>${priority.toFixed(1)}</priority>
  </url>`

const buildProductLink = (product: any): string => {
  const isVirtual = Boolean(product?.isVirtual)
  const prefix = isVirtual ? '/catalog/item' : '/catalog/products'

  if (isVirtual) {
    const id = product?.id ?? product?.prod_id
    const sku = product?.prod_analogsku || ''
    return `${BASE_URL}${prefix}/${id}?sku=${encodeURIComponent(sku)}`
  }

  const id = product?.prod_id
  const sku = product?.prod_sku || ''
  return `${BASE_URL}${prefix}/${id}?sku=${encodeURIComponent(sku)}`
}

export default class SitemapsController {
  public async generateSitemapIndex({ response }: HttpContextContract) {
    response.header('Content-Type', 'application/xml')
    response.type('application/xml')

    try {
      let productCount = 0

      // Пытаемся получить количество документов из MeiliSearch
      try {
        const stats = await meiliDB.client.index('products').getStats()
        productCount = Number(stats?.numberOfDocuments || 0)
      } catch (e) {
        // Фолбэк: считаем в БД
        const countResult = await Product.query().count('* as total')
        productCount = Number(countResult[0].$extras.total)
      }

      const productPages = Math.ceil(productCount / SITEMAP_PAGE_SIZE)

      let sitemapIndexXml =
        `<?xml version="1.0" encoding="UTF-8"?>\n<sitemapindex xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">`

      sitemapIndexXml += `\n  <sitemap>\n    <loc>${BASE_URL}/api/sitemap-static.xml</loc>\n    <lastmod>${new Date().toISOString()}</lastmod>\n  </sitemap>`

      for (let i = 1; i <= productPages; i++) {
        sitemapIndexXml += `\n  <sitemap>\n    <loc>${BASE_URL}/api/sitemap-products/${i}</loc>\n    <lastmod>${new Date().toISOString()}</lastmod>\n  </sitemap>`
      }

      sitemapIndexXml += `\n</sitemapindex>`
      return response.send(sitemapIndexXml)
    } catch (error) {
      console.error('Sitemap index generation error:', error)
      return response.internalServerError('Error generating sitemap index')
    }
  }

  public async generateStaticSitemap({ response }: HttpContextContract) {
    response.header('Content-Type', 'application/xml')
    response.type('application/xml')

    try {
      let content = ''
      const currentDate = new Date().toISOString()

      content += createUrlEntry(BASE_URL, currentDate, 'weekly', 1.0)

      const staticPages = await Page.query().whereIn('page_key', ['shipping', 'contacts', 'QA', 'brands', 'payment', 'price']).andWhere('page_locale', 'ru').select('page_key')
      for (const page of staticPages) {
        if (page.page_key) {
          content += createUrlEntry(`${BASE_URL}/${page.page_key}`, currentDate, 'monthly', 0.7)
        }
      }

      const categories = await Category.query().where('cat_active', true).andWhere('duplicate', 0).select('cat_url')
      for (const category of categories) {
        if (category.cat_url) {
          content += createUrlEntry(`${BASE_URL}/catalog/${category.cat_url}`, currentDate, 'weekly', 0.8)
        }
      }

      return response.send(createUrlset(content))
    } catch (error) {
      console.error('Static sitemap generation error:', error)
      return response.internalServerError('Error generating static sitemap')
    }
  }

  public async generateProductsSitemap({ response, params }: HttpContextContract) {
    const page = Number(params.page)
    if (Number.isNaN(page) || page < 1) {
      return response.badRequest('Invalid page number')
    }

    response.header('Content-Type', 'application/xml')
    response.type('application/xml')

    const currentDate = new Date().toISOString()

    try {
      const offset = (page - 1) * SITEMAP_PAGE_SIZE

      // 1) Пытаемся получить товары из MeiliSearch (включая виртуальные)
      const fields = ['id', 'isVirtual', 'prod_id', 'prod_sku', 'prod_analogsku']
      const index = meiliDB.client.index('products')

      let hits: any[] = []
      let fetched = 0
      while (fetched < SITEMAP_PAGE_SIZE) {
        const limit = Math.min(MEILI_CHUNK_SIZE, SITEMAP_PAGE_SIZE - fetched)
        const res: any = await index.search(null, {
          offset: offset + fetched,
          limit,
          attributesToRetrieve: fields,
          // sort: ['id:asc']
        })

        const chunk = Array.isArray(res?.hits) ? res.hits : []
        if (!chunk.length) break

        hits = hits.concat(chunk)
        fetched += chunk.length

        if (chunk.length < limit) break
      }

      if (hits.length) {
        let content = ''
        for (const product of hits) {
          const productUrl = buildProductLink(product)
          content += createUrlEntry(productUrl, currentDate, 'daily', 0.9)
        }
        return response.send(createUrlset(content))
      }

      // 2) Фолбэк: берем из БД (без виртуальных товаров)
      const items: Array<{ prod_id: number; prod_sku: string }> = await Database.from('products')
        .select('prod_id')
        .select('prod_sku')
        .orderBy('prod_id', 'asc')
        .limit(SITEMAP_PAGE_SIZE)
        .offset(offset)

      if (!items.length) {
        return response.notFound('Sitemap page not found')
      }

      let content = ''
      for (const product of items) {
        const productUrl = buildProductLink(product)
        content += createUrlEntry(productUrl, currentDate, 'daily', 0.9)
      }

      return response.send(createUrlset(content))
    } catch (error) {
      console.error(`Sitemap for products page ${page} generation error:`, error)
      return response.internalServerError('Error generating products sitemap')
    }
  }
}
