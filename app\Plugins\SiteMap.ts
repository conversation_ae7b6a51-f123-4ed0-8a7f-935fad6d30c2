import { getCategoryHrefByLocale } from "App/Helpers/getProductHrefByLocale"
import Category     from "App/Models/Category"
import Product      from "App/Models/Product"
import { $prisma } from "App/Services/Prisma"
import { meiliDB } from 'App/Plugins/MeiliSearch'

const convert = require('xml-js')

// sitemapGenerator.ts


const prisma = $prisma
const BASE_URL = 'https://mirsalnikov.ru'; // URL вашего сайта

// Максимальное количество URL в одном sitemap файле по протоколу
const SITEMAP_PAGE_SIZE = 50_000;

// Вспомогательные функции для ссылок
const buildProductLink = (product: any, baseUrl: string = BASE_URL): string => {
  const isVirtual = Boolean(product?.isVirtual)
  const prefix = isVirtual ? '/catalog/item' : '/catalog/products'
  if (isVirtual) {
    const id = product?.id ?? product?.prod_id
    const sku = product?.prod_analogsku || ''
    return `${baseUrl}${prefix}/${id}?sku=${encodeURIComponent(sku)}`
  }
  const id = product?.prod_id
  const sku = product?.prod_sku || ''
  return `${baseUrl}${prefix}/${id}?sku=${encodeURIComponent(sku)}`
}

const getBaseHostByLocale = (locale: string): string => {
  if (locale === 'ru') return 'https://mirsalnikov.ru'
  if (locale === 'en') return 'https://rumisota.eu'
  return `https://${locale}.rumisota.eu`
}

export class SiteMap {

    catalog
    categories: Array<Category>
    siteurl: string
    limit: number
    page: number

    constructor() {
        this.siteurl = 'https://mirsalnikov.ru/'
        this.categories = []
    }

    async loadProducts(page, limit, opts?: { source?: 'db' | 'meili' }) {
      // Если явно просим БД — пропускаем Meili и берем только основные товары
      if (opts?.source === 'db') {
        const _catalog = await Product
          .query()
          .select(['prod_id'])
          .orderBy('prod_count', 'desc')
          .orderBy('prod_id', 'desc')
          .paginate(page, limit)
        this.catalog = _catalog.toJSON().data
        console.log('this.catalog.lastPage: ', _catalog.lastPage)
        return
      }

      try {
        const fields = ['id', 'isVirtual', 'prod_id', 'prod_sku', 'prod_analogsku']
        const index = meiliDB.client.index('products')
        const hits: any[] = []
        let fetched = 0
        const offset = (page - 1) * limit
        const CHUNK = 1000
        while (fetched < limit) {
          const res: any = await index.search(null, {
            offset: offset + fetched,
            limit: Math.min(CHUNK, limit - fetched),
            attributesToRetrieve: fields,
            sort: ['id:asc']
          })
          const chunk = Array.isArray(res?.hits) ? res.hits : []
          if (!chunk.length) break
          hits.push(...chunk)
          fetched += chunk.length
          if (chunk.length < CHUNK) break
        }
        if (hits.length) {
          this.catalog = hits
          return
        }
      } catch (e) {
        // ignore, fallback below
      }

      // Fallback: БД (без виртуальных товаров)
      const _catalog = await Product
        .query()
        .select(['prod_id', 'prod_sku'])
        .orderBy('prod_count', 'desc')
        .orderBy('prod_id', 'desc')
        .paginate(page, limit)

      this.catalog = _catalog.toJSON().data
      console.log('this.catalog.lastPage: ', _catalog.lastPage)
    }

    async loadCategories(page, limit) {
        if (page == 1) {
            this.categories = await Category.query().select('*').where('cat_active', 1)
        }
    }

    async make(page = 1, limit = 10000) {
        const today = new Date()
        const fulldate = today.getFullYear() + '-' + today.getMonth() + '-' + today.getDate()

        await this.loadProducts(page, limit)
        await this.loadCategories(page, limit)

        let categoriesData = this.categories.map(category => {
            return {
                loc: this.siteurl + 'catalog/' + category.cat_url,
                lastmod: fulldate,
                priority: 0.5,
                changefreq: 'weekly'
            }
        })

        let productsData = this.catalog.map(product => {
            return {
                loc: buildProductLink(product, BASE_URL),
                lastmod: fulldate,
                priority: 0.3,
                changefreq: 'daily'
            }
        })


        const json = {
            _declaration: {
                _attributes: {
                    "version": "1.0", "encoding": "utf-8"
                }
            },
            'urlset': {
                _attributes: {
                    xmlns: 'http://www.sitemaps.org/schemas/sitemap/0.9'
                },
                url: [...categoriesData, ...productsData]
            }
        }

        const options = { compact: true, ignoreComment: true, spaces: 4 }

        let result = convert.json2xml(json, options)

        return result
    }

    async makeGoogle(page = 1, limit = 45000, locale = 'en') {

        const today = new Date()
        const fulldate = today.getFullYear() + '-' + today.getMonth() + '-' + today.getDate()

        await this.loadProducts(page, limit, { source: locale === 'en' ? 'db' : 'meili' })
        await this.loadCategories(page, limit)

        let categoriesData = this.categories.map(category => {
            return {
                loc: getCategoryHrefByLocale(locale) + (locale == 'ru' ? (category.cat_url) : (category.$extras['cat_url_' + locale])),
                'xhtml:link': {
                    _attributes: {
                        rel: 'alternate',
                        hreflang: locale, // 'en' || 'de' ..etc
                        href: getCategoryHrefByLocale(locale) + (locale == 'ru' ? (category.cat_url) : (category.$extras['cat_url_' + locale]))
                    }
                },
                lastmod: today.toJSON().split('T')[0],
                changefreq: 'weekly',
                priority: 0.3,
            }
        })

        const base = getBaseHostByLocale(locale)
        let productsData = this.catalog.map((product: any) => {
            const loc = locale === 'en'
              ? `${base}/catalog/product/${product?.prod_id}`
              : buildProductLink(product, base)
            return {
                loc,
                'xhtml:link': {
                    _attributes: {
                        rel: 'alternate',
                        hreflang: locale,
                        href: loc
                    }
                },
                lastmod: today.toJSON().split('T')[0],
                priority: 0.5,
                changefreq: 'daily'
            }
        })

        const json = {
            _declaration: {
                _attributes: {
                    "version": "1.0", "encoding": "utf-8"
                }
            },
            'urlset': {
                _attributes: {
                    xmlns: 'http://www.sitemaps.org/schemas/sitemap/0.9',
                    'xmlns:xhtml': "http://www.w3.org/1999/xhtml"
                },
                url: [...categoriesData, ...productsData]
            }
        }

        const options = { compact: true, ignoreComment: true, spaces: 4 }

        let result = convert.json2xml(json, options)

        return result


    }
}
